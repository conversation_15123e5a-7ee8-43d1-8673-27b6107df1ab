package com.chis.zyjk.bigdata.alert.pojo.dto;

import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordLogPO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordNoticeLogPO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预警处理结果类
 */
@Data
public class AlertProcessResultDTO {
    /**
     * 预警记录去重键值
     */
    private List<String> deduplicationKeyList = new ArrayList<>();

    /**
     * 预警记录DTO映射
     * key: 预警去重键值
     * value: 预警记录DTO
     */
    private Map<String, List<AlertRecordDTO>> alertRecordMap = new HashMap<>();

    /**
     * 新增预警记录主表记录
     */
    private List<AlertRecordPO> createList = new ArrayList<>();
    /**
     * 更新预警记录主表记录
     */
    private List<AlertRecordPO> updateList = new ArrayList<>();
    /**
     * 处置预警记录主表记录
     */
    private List<AlertRecordPO> disposeList = new ArrayList<>();
    /**
     * 所有预警记录子表记录
     */
    private List<AlertRecordLogPO> logList = new ArrayList<>();
    /**
     * 所有预警记录通知子表记录
     */
    private List<AlertRecordNoticeLogPO> noticeLogList = new ArrayList<>();
}

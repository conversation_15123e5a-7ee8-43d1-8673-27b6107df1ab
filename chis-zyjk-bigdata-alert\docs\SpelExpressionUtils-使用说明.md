# SpelExpressionUtils 工具类使用说明

## 概述

`SpelExpressionUtils` 是一个强大的表达式评估工具类，提供了类似 Spring Expression Language (SpEL) 的功能，用于在 LiteFlow 组件中进行条件判断和数据过滤。

## 主要特性

- **基本比较操作**：支持 `==`, `!=`, `>`, `<`, `>=`, `<=` 等比较操作符
- **逻辑操作**：支持 `&&`, `||`, `!` 等逻辑操作符
- **字符串操作**：支持 `contains`, `startsWith`, `endsWith`, `matches` 等字符串操作
- **集合操作**：支持 `in`, `not in` 等集合包含判断
- **嵌套属性访问**：支持通过点号访问嵌套对象属性，如 `user.profile.name`
- **上下文变量**：支持通过 `${...}` 语法访问 LiteFlow 上下文变量
- **类型自动转换**：自动处理不同数据类型之间的比较
- **错误容错**：表达式解析错误时返回 `false` 而不是抛出异常

## 核心方法

### evaluateCondition

```java
public static boolean evaluateCondition(String condition, Object inputData, DefaultContext context)
```

**参数说明：**
- `condition`: 条件表达式字符串
- `inputData`: 输入数据对象（通常是 JSONObject 或 POJO）
- `context`: LiteFlow 上下文对象

**返回值：**
- `boolean`: 表达式评估结果

## 表达式语法

### 1. 基本比较操作

```java
// 数值比较
"age > 18"
"score >= 60"
"price <= 100.0"
"count == 5"
"status != 0"

// 字符串比较
"name == '张三'"
"department != 'IT'"
```

### 2. 逻辑操作

```java
// AND 操作
"age > 18 && score >= 60"

// OR 操作
"department == 'IT' || department == 'HR'"

// NOT 操作
"!active"
"!(age < 18)"

// 复合逻辑
"(age > 18 && score >= 60) || (level == '高级' && experience > 3)"
```

### 3. 字符串操作

```java
// 包含检查
"name contains '张'"

// 开始检查
"email startsWith 'admin'"

// 结束检查
"filename endsWith '.pdf'"

// 正则匹配
"phone matches '^1[3-9]\\d{9}$'"
```

### 4. 集合操作

```java
// 值在集合中
"department in ['IT', 'HR', 'Finance']"

// 值不在集合中
"status not in ['deleted', 'archived']"
```

### 5. 嵌套属性访问

```java
// 访问嵌套对象属性
"user.profile.level == '高级'"
"order.customer.address.city == '北京'"
"config.database.timeout > 5000"
```

### 6. 上下文变量

```java
// 使用上下文变量（需要 VariableReplacerUtils 支持）
"age > ${global.minAge}"
"department == ${config.defaultDepartment}"
"score >= ${nodeId.threshold.passing}"
```

### 7. 数据类型支持

```java
// 字符串字面量
"name == 'John'"
"status == \"active\""

// 数值字面量
"age == 25"
"price == 99.99"

// 布尔字面量
"active == true"
"deleted == false"

// null 值
"description == null"
```

## 使用示例

### 在 FilterJudgeCmp 中使用

```java
public class FilterJudgeCmp extends NodeComponent {
    @Override
    public void process() {
        // 获取输入数据和上下文
        Object inputData = this.getSlot().getInput();
        DefaultContext context = this.getContextBean(DefaultContext.class);
        
        // 定义过滤条件
        String condition = "age > 18 && department == 'IT' && profile.level contains '级'";
        
        // 评估条件
        boolean result = SpelExpressionUtils.evaluateCondition(condition, inputData, context);
        
        if (result) {
            // 条件满足的处理逻辑
            System.out.println("条件满足，继续处理");
        } else {
            // 条件不满足的处理逻辑
            System.out.println("条件不满足，跳过处理");
        }
    }
}
```

### 在其他组件中使用

```java
// 数据过滤示例
List<JSONObject> dataList = getDataList();
List<JSONObject> filteredData = new ArrayList<>();

String filterCondition = "score >= 80 && active == true";

for (JSONObject data : dataList) {
    if (SpelExpressionUtils.evaluateCondition(filterCondition, data, context)) {
        filteredData.add(data);
    }
}
```

## 最佳实践

### 1. 条件表达式设计

- **简洁明了**：避免过于复杂的表达式，必要时可以拆分为多个简单条件
- **性能考虑**：将最可能为 false 的条件放在 AND 操作的前面
- **可读性**：使用有意义的字段名和清晰的逻辑结构

### 2. 错误处理

- **容错设计**：表达式解析失败时会返回 `false`，不会抛出异常
- **日志记录**：工具类内部会记录错误日志，便于调试
- **测试验证**：在生产环境使用前，充分测试各种边界情况

### 3. 性能优化

- **缓存结果**：对于相同的表达式和数据，可以考虑缓存评估结果
- **表达式预编译**：对于频繁使用的表达式，可以考虑预编译优化
- **数据结构**：使用合适的数据结构（如 JSONObject）以提高属性访问效率

## 注意事项

1. **字符串字面量**：必须使用单引号或双引号包围
2. **数值比较**：自动进行类型转换，支持整数和浮点数混合比较
3. **null 值处理**：null 值参与比较时遵循 Java 的 null 比较规则
4. **正则表达式**：使用 `matches` 操作符时，右侧应为有效的正则表达式
5. **上下文变量**：依赖 `VariableReplacerUtils` 进行变量替换，确保相关工具类可用

## 扩展说明

如需添加新的操作符或功能，可以在 `SpelExpressionUtils` 类中扩展相应的方法：

- `compareValues()`: 添加新的比较操作符
- `getExpressionValue()`: 添加新的字面量类型支持
- `evaluateCondition()`: 添加新的表达式预处理逻辑

## 版本历史

- **v1.0**: 初始版本，支持基本的表达式评估功能
- **v1.1**: 从 FilterJudgeCmp 中重构为独立工具类，提高代码复用性

package com.chis.zyjk.bigdata.alert.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.zyjk.core.common.pojo.ZyjkPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警规则主表
 */
@Data
@TableName("alert_rule")
@EqualsAndHashCode(callSuper = false)
public class AlertRulePO extends ZyjkPO {

    /**
     * xxljob任务ID
     */
    private Integer xxljobTaskId;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 去重主键表达式 如{org_id}_{person_id}
     */
    private String dedupKeyExpression;

    /**
     * 预警值表达式 如{org_id}_{person_id}
     */
    private String alertValueExpression;

    /**
     * 流程配置表达式
     */
    private String liteFlowConfig;

    /**
     * 节点配置json
     */
    private String liteFlowNodeConfig;

    /**
     * 状态：0停用 1启用 2待执行 3执行中 4异常
     */
    private Integer status;

    /**
     * 版本:自动加1
     */
    private Integer vision;
}

package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.collection.CollUtil;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowCmpException;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.chis.zyjk.bigdata.alert.liteflow.utils.SpELExpressionUtils;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeBooleanComponent;
import com.yomahub.liteflow.exception.NoSuchContextBeanException;
import com.yomahub.liteflow.log.LFLog;
import com.yomahub.liteflow.log.LFLoggerManager;

import java.util.ArrayList;
import java.util.List;

/**
 * IF条件判断组件
 * <p>
 * <b>IF(ifJudge, ..., ...)</b>
 * <p>
 * 或
 * <p>
 * <b>IF(ifJudge, ...).ELSE(...)</b>
 * <p>
 * 或
 * <p>
 * <b>IF(ifJudge, ...).ELIF(ifJudge, ...).ELSE(...)</b>
 */
@LiteflowComponent(id = "ifJudge", name = "IF条件判断组件")
public class IfJudgeCmp extends NodeBooleanComponent {
    private final LFLog logger = LFLoggerManager.getLogger(FlowExecutor.class);

    @Override
    public boolean processBoolean() {
        String nodeId = this.getNodeId();
        String name = this.getName();
        String tag = this.getTag();
        if (ObjectUtil.isEmpty(tag)) {
            throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_TAG_MISSING, this);
        }

        try {
            CmpContext cmpContext = this.getContextBean(CmpContext.class);
            // 上下文中获取组件配置
            JSONObject config = ContextHelper.getOrCreateContextConfig(cmpContext, tag);
            checkConfig(config);
            boolean b = SpELExpressionUtils.evaluateCondition(config.getStr("condition"), this.getSlot().getContextBeanList());
            return b;
        } catch (LiteFlowCmpException e) {
            throw e;
        } catch (NoSuchContextBeanException e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_CONFIG_CONTEXT_EMPTY,
                    this
            );
        } catch (Exception e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_EXECUTION_FAILED,
                    name + "执行失败: " + e.getMessage(),
                    this
            );
        }
    }

    /**
     * 验证组件必需配置
     *
     * @param config 组件配置
     */
    public void checkConfig(JSONObject config) {
        List<String> paramList = new ArrayList<>();
        if (ObjectUtil.isEmpty(config)) {
            paramList.add("condition");
        } else {
            if (!config.containsKey("condition") && ObjectUtil.isEmpty(config.get("condition"))) {
                paramList.add("condition");
            }
        }

        if (paramList.isEmpty()) {
            return;
        }

        throw LiteFlowExceptionHelper.createNodeException(
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED,
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED.getDesc() + "：缺少" + CollUtil.join(paramList, "、") + "配置",
                this
        );
    }
}

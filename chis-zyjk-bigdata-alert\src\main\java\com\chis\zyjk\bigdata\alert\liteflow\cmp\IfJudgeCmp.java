package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeBooleanComponent;
import com.chis.zyjk.bigdata.alert.liteflow.utils.SpELExpressionUtils;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;

/**
 * IF条件判断组件
 * <p>
 * <b>IF(ifJudge, ..., ...)</b>
 * <p>
 * 或
 * <p>
 * <b>IF(ifJudge, ...).ELSE(...)</b>
 * <p>
 * 或
 * <p>
 * <b>IF(ifJudge, ...).ELIF(ifJudge, ...).ELSE(...)</b>
 */
@LiteflowComponent(id = "ifJudge", name = "IF条件判断组件")
public class IfJudgeCmp extends ChisNodeBooleanComponent<AlertGlobalContext, AlertCmpContext> {
    @Override
    public boolean doProcessBoolean() {
        String condition = VariableReplacerUtils.replaceVariables(this.tag, config.getStr("condition"), contextBeanList, "null");
        boolean b = SpELExpressionUtils.evaluateCondition(condition, contextBeanList);
        return b;
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"condition"};
    }
}

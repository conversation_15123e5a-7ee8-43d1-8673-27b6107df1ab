package com.chis.zyjk.bigdata.alert.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.zyjk.core.common.pojo.ZyjkPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警记录表
 */
@Data
@TableName("alert_record")
@EqualsAndHashCode(callSuper = false)
public class AlertRecordPO extends ZyjkPO {

    /**
     * 预警规则编码，关联alert_rule.rule_code
     */
    private String ruleCode;

    /**
     * 去重键值
     */
    private String dedupValue;

    /**
     * 最新预警变更类型：CREATE/UPDATE/DISPOSE
     */
    private String changeType;

    /**
     * 最新预警级别编码
     */
    private String alertLevel;

    /**
     * 最新预警值
     */
    private String alertValue;

    /**
     * 状态：0未处置 1已处置 2已忽略
     */
    private Integer status;

    /**
     * 最新预警内容
     */
    private String alertContent;

    /**
     * 最新预警自定义JSON
     */
    private String alertJson;

    /**
     * 最新预警源数据JSON
     */
    private String sourceData;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.zyjk.bigdata.alert.mapper.AlertRecordNoticeLogMapper">

    <resultMap id="BaseResultMap" type="com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordNoticeLogPO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="alertRecordId" column="alert_record_id" jdbcType="VARCHAR"/>
        <result property="alertRecordLogId" column="alert_record_log_id" jdbcType="VARCHAR"/>
        <result property="ruleCode" column="rule_code" jdbcType="VARCHAR"/>
        <result property="dedupKey" column="dedup_key" jdbcType="VARCHAR"/>
        <result property="changeType" column="change_type" jdbcType="VARCHAR"/>
        <result property="alertLevel" column="alert_level" jdbcType="VARCHAR"/>
        <result property="alertValue" column="alert_value" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="alertContent" column="alert_content" jdbcType="LONGVARCHAR"/>
        <result property="alertJson" column="alert_json" jdbcType="LONGVARCHAR"/>
        <result property="sourceData" column="source_data" jdbcType="LONGVARCHAR"/>
        <result property="noticeContent" column="notice_content" jdbcType="LONGVARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, alert_record_id, alert_record_log_id, rule_code, dedup_key, change_type,
        alert_level, alert_value, status, alert_content, alert_json, source_data,
        notice_content, revision, del_flag, create_time, create_by, update_time, update_by
    </sql>

</mapper>

package com.chis.zyjk.bigdata.alert.liteflow.core;

import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeBooleanComponent;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.log.LFLog;
import com.yomahub.liteflow.log.LFLoggerManager;

public abstract class ChisNodeBooleanComponent extends NodeBooleanComponent {
    protected final LFLog logger = LFLoggerManager.getLogger(FlowExecutor.class);

    @Override
    public void afterProcess() {
        super.afterProcess();
        CmpContext cmpContext = this.getContextBean(CmpContext.class);
        if (ObjectUtil.isNotEmpty(cmpContext)) {
            String contextKey = "[" + this.getTag() + ":" + this.getNodeId() + ":" + this.getName() + "]";
            logger.debug("{}上下文: {}", contextKey, cmpContext.getDataMap().toString());
        }
    }
}

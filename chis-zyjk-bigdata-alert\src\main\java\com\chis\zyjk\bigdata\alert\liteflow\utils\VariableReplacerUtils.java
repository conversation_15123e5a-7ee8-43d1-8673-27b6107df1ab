package com.chis.zyjk.bigdata.alert.liteflow.utils;

import cn.hutool.core.lang.Tuple;
import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.yomahub.liteflow.slot.DefaultContext;
import com.yomahub.liteflow.util.LiteflowContextRegexMatcher;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 变量替换工具类
 * <p>
 * 支持的变量语法：
 * <p>- ${global.field} - 引用全局上下文数据
 * <p>- ${nodeId.currentItem.field} - 引用指定节点的当前项数据
 * <p>- ${nodeId.currentData} - 引用指定节点的当前批次数据
 * <p>- ${nodeId.loopIndex} - 引用指定节点的循环索引
 * <p>- ${uuid()} - 生成UUID
 * <p>- ${timestamp()} - 生成时间戳
 * <p>- ${date('yyyy-MM-dd')} - 格式化当前日期
 */
@Slf4j
public final class VariableReplacerUtils {

    // 变量表达式正则模式
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    // 函数调用正则模式
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("(\\w+)\\(([^)]*)\\)");

    /**
     * 替换字符串中的所有变量
     *
     * @param template 包含变量的模板字符串
     * @param context  LiteFlow上下文
     * @return 替换后的字符串
     */
    public static String replaceVariables(String template, List<Tuple> context) {
        if (template == null || template.isEmpty()) {
            return template;
        }

        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String expression = matcher.group(1);
            Object value = resolveExpression(expression, context);
            matcher.appendReplacement(result, Matcher.quoteReplacement(Convert.toStr(value, "null")));
        }

        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 解析单个表达式
     *
     * @param expression 表达式内容（不包含${}）
     * @param context    LiteFlow上下文
     * @return 解析后的值
     */
    public static Object resolveExpression(String expression, List<Tuple> context) {
        try {
            // 检查是否是函数调用
            Matcher functionMatcher = FUNCTION_PATTERN.matcher(expression);
            if (functionMatcher.matches()) {
                String functionName = functionMatcher.group(1);
                String params = functionMatcher.group(2);
                return callFunction(functionName, params);
            }
            return LiteflowContextRegexMatcher.searchContext(context, expression);
        } catch (Exception e) {
            System.err.println("解析变量表达式失败: " + expression + ", " + e.getMessage());
            return "${" + expression + "}"; // 返回原始表达式
        }
    }

    /**
     * 获取全局上下文值
     */
    private static String getGlobalValue(String path, DefaultContext context) {
        Object globalData = context.getData("global");
        if (globalData instanceof JSONObject) {
            JSONObject global = (JSONObject) globalData;
            Object value = getNestedValue(global, path);
            return value != null ? value.toString() : "";
        }
        return "";
    }

    /**
     * 获取节点上下文值
     */
    private static String getNodeValue(String nodeId, String path, DefaultContext context) {
        Object nodesData = context.getData("nodes");
        if (nodesData instanceof JSONObject) {
            JSONObject nodes = (JSONObject) nodesData;
            JSONObject nodeData = nodes.getJSONObject(nodeId);
            if (nodeData != null) {
                Object value = getNestedValue(nodeData, path);
                return value != null ? value.toString() : "";
            }
        }
        return "";
    }

    /**
     * 获取嵌套路径的值
     */
    public static Object getNestedValue(JSONObject json, String path) {
        String[] parts = path.split("\\.");
        Object current = json;

        for (String part : parts) {
            if (current instanceof JSONObject) {
                current = ((JSONObject) current).get(part);
            } else {
                return null;
            }
        }

        return current;
    }

    /**
     * 调用内置函数
     */
    private static String callFunction(String functionName, String params) {
        switch (functionName) {
            case "uuid":
                return UUID.randomUUID().toString();
            case "timestamp":
                return String.valueOf(System.currentTimeMillis());
            case "date":
                String format = params.replaceAll("'", "");
                if (format.isEmpty()) {
                    format = "yyyy-MM-dd HH:mm:ss";
                }
                return LocalDateTime.now().format(DateTimeFormatter.ofPattern(format));
            default:
                System.err.println("未知的函数: " + functionName);
                return "${" + functionName + "(" + params + ")}";
        }
    }
}

# FilterJudgeCmp 使用说明

## 概述

`FilterJudgeCmp` 是一个LiteFlow组件，用于根据配置的条件表达式对输入数据进行过滤。该组件支持强大的SpEL（Spring Expression Language）表达式语法，可以处理复杂的数据过滤逻辑。

## 配置参数

### 必需参数

- `inputDataPath`: 输入数据在上下文中的路径
- `condition`: 过滤条件表达式

### 配置示例

```json
{
  "inputDataPath": "nodeA.data.currentData",
  "condition": "age >= 18 && status == 'active'"
}
```

## 表达式语法

### 基本比较操作

- `==`: 等于
- `!=`: 不等于
- `>`: 大于
- `<`: 小于
- `>=`: 大于等于
- `<=`: 小于等于

```json
{
  "condition": "age >= 18"
}
```

### 字符串操作

- `contains`: 包含
- `startsWith`: 以...开始
- `endsWith`: 以...结束
- `matches`: 正则匹配

```json
{
  "condition": "name contains '张' && email endsWith '@example.com'"
}
```

### 逻辑操作

- `&&`: 逻辑与
- `||`: 逻辑或
- `!`: 逻辑非

```json
{
  "condition": "(age >= 18 && age <= 65) || isVip == true"
}
```

### 集合操作

- `in`: 在集合中
- `not in`: 不在集合中

```json
{
  "condition": "status in ['active', 'pending'] && department not in ['temp', 'intern']"
}
```

### 嵌套属性访问

支持点号分隔的嵌套属性访问：

```json
{
  "condition": "user.profile.age >= 18 && user.address.city == '北京'"
}
```

### 上下文变量

支持通过 `${...}` 语法访问上下文变量：

```json
{
  "condition": "score >= ${global.passingScore} && department == ${nodeA.data.targetDepartment}"
}
```

## 数据类型支持

### 数值类型
- 整数：`25`, `-10`
- 浮点数：`85.5`, `-3.14`

### 字符串类型
- 单引号：`'张三'`
- 双引号：`"active"`

### 布尔类型
- `true`
- `false`

### 空值
- `null`

## 使用示例

### 示例1：简单年龄过滤

```json
{
  "inputDataPath": "userList",
  "condition": "age >= 18"
}
```

### 示例2：复合条件过滤

```json
{
  "inputDataPath": "employeeList",
  "condition": "(age >= 25 && age <= 55) && (department == 'IT' || department == 'Finance') && status == 'active'"
}
```

### 示例3：字符串匹配过滤

```json
{
  "inputDataPath": "customerList",
  "condition": "name contains '公司' && email endsWith '@company.com' && phone matches '^1[3-9]\\d{9}$'"
}
```

### 示例4：使用上下文变量

```json
{
  "inputDataPath": "scoreList",
  "condition": "score >= ${global.minScore} && subject == ${nodeA.data.targetSubject}"
}
```

### 示例5：集合操作

```json
{
  "inputDataPath": "orderList",
  "condition": "status in ['paid', 'shipped', 'delivered'] && paymentMethod not in ['cash', 'check']"
}
```

## 输出结果

组件执行后会在上下文中设置两个字段：

- `matchedData`: 符合条件的数据列表
- `unmatchedData`: 不符合条件的数据列表

### 访问输出数据

```java
// 获取匹配的数据
List<Object> matchedData = (List<Object>) ContextHelper.getContextData(tag, "matchedData", context);

// 获取未匹配的数据
List<Object> unmatchedData = (List<Object>) ContextHelper.getContextData(tag, "unmatchedData", context);
```

## 错误处理

- 空条件表达式默认返回 `true`（所有数据都匹配）
- 表达式解析错误时返回 `false`（数据不匹配）
- 访问不存在的属性时返回 `null`
- 类型不匹配的比较操作会抛出异常

## 性能建议

1. **简化表达式**: 避免过于复杂的嵌套表达式
2. **提前退出**: 利用 `&&` 和 `||` 的短路特性
3. **索引优化**: 对于大数据量，考虑在数据库层面进行预过滤
4. **缓存结果**: 对于重复的条件表达式，可以考虑缓存解析结果

## 注意事项

1. 字符串比较区分大小写
2. 正则表达式需要转义特殊字符
3. 上下文变量必须在表达式评估前设置
4. 集合操作中的数组格式为 `['item1', 'item2']`
5. 嵌套属性访问时，中间任何一层为 `null` 都会导致整个表达式返回 `null`

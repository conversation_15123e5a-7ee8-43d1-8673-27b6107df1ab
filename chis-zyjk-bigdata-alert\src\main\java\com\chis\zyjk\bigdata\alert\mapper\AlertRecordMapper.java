package com.chis.zyjk.bigdata.alert.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 预警记录表Mapper
 */
public interface AlertRecordMapper extends BaseMapper<AlertRecordPO> {

    /**
     * 根据自定义条件查询记录列表
     *
     * @param conditions 查询条件Map
     * @return 预警记录列表
     */
    List<AlertRecordPO> listByConditions(@Param("conditions") Map<String, Object> conditions);

    /**
     * 根据自定义条件查询记录列表且根据分组及排序仅查询每组前n条的数据
     *
     * @param params 查询参数Map，包含conditions、groupByField、orderByField、orderDesc、topN
     * @return 预警记录列表
     */
    List<AlertRecordPO> listTopNByGroup(@Param("params") Map<String, Object> params);

}

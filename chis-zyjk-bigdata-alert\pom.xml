<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.chis.zyjk</groupId>
        <artifactId>chis-zyjk-bigdata-platform</artifactId>
        <version>1.0.0.ALPHA</version>
    </parent>

    <artifactId>chis-zyjk-bigdata-alert</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.chis.zyjk</groupId>
            <artifactId>chis-zyjk-core-common</artifactId>
            <version>1.0.0.ALPHA</version>
        </dependency>

        <dependency>
            <groupId>com.chis.zyjk</groupId>
            <artifactId>chis-zyjk-bigdata-common</artifactId>
            <version>1.0.0.ALPHA</version>
        </dependency>

        <!-- liteflow -->
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
            <version>2.13.2</version>
            <exclusions>
                <!-- 排除FastJSON，避免与框架JSON工具类冲突 -->
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>
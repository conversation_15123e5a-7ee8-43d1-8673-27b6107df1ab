package com.chis.zyjk.bigdata.alert.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.zyjk.core.common.pojo.ZyjkPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警记录预警子表
 */
@Data
@TableName("alert_record_log")
@EqualsAndHashCode(callSuper = false)
public class AlertRecordLogPO extends ZyjkPO {

    /**
     * 关联alert_record.id
     */
    private String alertRecordId;

    /**
     * 冗余字段，预警规则编码，关联alert_rule.rule_code
     */
    private String ruleCode;

    /**
     * 去重键值
     */
    private String dedupKey;

    /**
     * 变更类型：CREATE/UPDATE/DISPOSE
     */
    private String changeType;

    /**
     * 预警级别编码
     */
    private String alertLevel;

    /**
     * 预警值
     */
    private String alertValue;

    /**
     * 状态：0未处置 1已处置 2已忽略
     */
    private Integer status;

    /**
     * 预警内容
     */
    private String alertContent;

    /**
     * 预警自定义JSON
     */
    private String alertJson;

    /**
     * 预警源数据JSON
     */
    private String sourceData;
}

package com.chis.zyjk.bigdata.alert.enums;

import com.chis.zyjk.core.common.enums.base.StringCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据处理类型
 */
@Getter
@AllArgsConstructor
public enum DataProcessType implements StringCodeEnum {
    /**
     * 业务记录
     */
    BUSINESS("BUSINESS", "新增"),
    /**
     * 预警记录
     */
    ALERT("ALERT", "更新");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    @Override
    public String getEnumDesc() {
        return "数据处理类型";
    }
}

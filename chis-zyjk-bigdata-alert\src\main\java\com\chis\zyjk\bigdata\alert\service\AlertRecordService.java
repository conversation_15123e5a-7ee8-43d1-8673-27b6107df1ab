package com.chis.zyjk.bigdata.alert.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chis.zyjk.bigdata.alert.mapper.AlertRecordMapper;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 预警记录服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertRecordService {

    private final AlertRecordMapper alertRecordMapper;

    /**
     * 根据ID查询预警记录
     *
     * @param id 主键ID
     * @return 预警记录
     */
    public AlertRecordPO getById(String id) {
        return alertRecordMapper.selectById(id);
    }

    /**
     * 根据规则编码和去重值查询预警记录
     *
     * @param ruleCode 规则编码
     * @param dedupValue 去重值
     * @return 预警记录
     */
    public AlertRecordPO getByRuleCodeAndDedupValue(String ruleCode, String dedupValue) {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordPO::getRuleCode, ruleCode)
                .eq(AlertRecordPO::getDedupValue, dedupValue)
                .eq(AlertRecordPO::getDelFlag, "0");
        return alertRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 根据规则编码查询预警记录
     *
     * @param ruleCode 规则编码
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listByRuleCode(String ruleCode) {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordPO::getRuleCode, ruleCode)
                .eq(AlertRecordPO::getDelFlag, "0")
                .orderByDesc(AlertRecordPO::getUpdateTime);
        return alertRecordMapper.selectList(queryWrapper);
    }

    /**
     * 查询未处置的预警记录
     *
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listUnprocessed() {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordPO::getStatus, 0)
                .eq(AlertRecordPO::getDelFlag, "0")
                .orderByDesc(AlertRecordPO::getCreateTime);
        return alertRecordMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询预警记录
     *
     * @param page 分页参数
     * @param ruleCode 规则编码（可选）
     * @param alertLevel 预警级别（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    public Page<AlertRecordPO> page(Page<AlertRecordPO> page, String ruleCode, String alertLevel, Integer status) {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ruleCode != null, AlertRecordPO::getRuleCode, ruleCode)
                .eq(alertLevel != null, AlertRecordPO::getAlertLevel, alertLevel)
                .eq(status != null, AlertRecordPO::getStatus, status)
                .eq(AlertRecordPO::getDelFlag, "0")
                .orderByDesc(AlertRecordPO::getUpdateTime);
        return alertRecordMapper.selectPage(page, queryWrapper);
    }

    /**
     * 保存预警记录
     *
     * @param alertRecord 预警记录
     * @return 是否成功
     */
    public boolean save(AlertRecordPO alertRecord) {
        return alertRecordMapper.insert(alertRecord) > 0;
    }

    /**
     * 更新预警记录
     *
     * @param alertRecord 预警记录
     * @return 是否成功
     */
    public boolean updateById(AlertRecordPO alertRecord) {
        return alertRecordMapper.updateById(alertRecord) > 0;
    }

    /**
     * 更新预警记录状态
     *
     * @param id 主键ID
     * @param status 状态
     * @return 是否成功
     */
    public boolean updateStatus(String id, Integer status) {
        LambdaUpdateWrapper<AlertRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AlertRecordPO::getStatus, status)
                .eq(AlertRecordPO::getId, id);
        return alertRecordMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 批量更新预警记录状态
     *
     * @param ids 主键ID列表
     * @param status 状态
     * @return 是否成功
     */
    public boolean batchUpdateStatus(List<String> ids, Integer status) {
        LambdaUpdateWrapper<AlertRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AlertRecordPO::getStatus, status)
                .in(AlertRecordPO::getId, ids);
        return alertRecordMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 批量新增预警记录
     *
     * @param alertRecords 预警记录列表
     * @return 是否成功
     */
    public boolean saveBatch(List<AlertRecordPO> alertRecords) {
        if (alertRecords == null || alertRecords.isEmpty()) {
            return true;
        }

        for (AlertRecordPO alertRecord : alertRecords) {
            alertRecordMapper.insert(alertRecord);
        }
        return true;
    }

    /**
     * 批量更新预警记录
     *
     * @param alertRecords 预警记录列表
     * @return 是否成功
     */
    public boolean updateBatchById(List<AlertRecordPO> alertRecords) {
        if (alertRecords == null || alertRecords.isEmpty()) {
            return true;
        }

        for (AlertRecordPO alertRecord : alertRecords) {
            alertRecordMapper.updateById(alertRecord);
        }
        return true;
    }

    /**
     * 根据自定义条件查询记录列表
     *
     * @param conditions 查询条件Map
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listByConditions(Map<String, Object> conditions) {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();

        // 基础条件：未删除
        queryWrapper.eq(AlertRecordPO::getDelFlag, "0");

        if (conditions != null && !conditions.isEmpty()) {
            // 规则编码
            if (conditions.containsKey("ruleCode") && StringUtils.hasText((String) conditions.get("ruleCode"))) {
                queryWrapper.eq(AlertRecordPO::getRuleCode, conditions.get("ruleCode"));
            }

            // 规则编码列表
            if (conditions.containsKey("ruleCodes") && conditions.get("ruleCodes") instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> ruleCodes = (List<String>) conditions.get("ruleCodes");
                if (!ruleCodes.isEmpty()) {
                    queryWrapper.in(AlertRecordPO::getRuleCode, ruleCodes);
                }
            }

            // 去重值
            if (conditions.containsKey("dedupValue") && StringUtils.hasText((String) conditions.get("dedupValue"))) {
                queryWrapper.eq(AlertRecordPO::getDedupValue, conditions.get("dedupValue"));
            }

            // 去重值列表
            if (conditions.containsKey("dedupValues") && conditions.get("dedupValues") instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> dedupValues = (List<String>) conditions.get("dedupValues");
                if (!dedupValues.isEmpty()) {
                    queryWrapper.in(AlertRecordPO::getDedupValue, dedupValues);
                }
            }

            // 变更类型
            if (conditions.containsKey("changeType") && StringUtils.hasText((String) conditions.get("changeType"))) {
                queryWrapper.eq(AlertRecordPO::getChangeType, conditions.get("changeType"));
            }

            // 预警级别
            if (conditions.containsKey("alertLevel") && StringUtils.hasText((String) conditions.get("alertLevel"))) {
                queryWrapper.eq(AlertRecordPO::getAlertLevel, conditions.get("alertLevel"));
            }

            // 预警级别列表
            if (conditions.containsKey("alertLevels") && conditions.get("alertLevels") instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> alertLevels = (List<String>) conditions.get("alertLevels");
                if (!alertLevels.isEmpty()) {
                    queryWrapper.in(AlertRecordPO::getAlertLevel, alertLevels);
                }
            }

            // 状态
            if (conditions.containsKey("status") && conditions.get("status") != null) {
                queryWrapper.eq(AlertRecordPO::getStatus, conditions.get("status"));
            }

            // 状态列表
            if (conditions.containsKey("statusList") && conditions.get("statusList") instanceof List) {
                @SuppressWarnings("unchecked")
                List<Integer> statusList = (List<Integer>) conditions.get("statusList");
                if (!statusList.isEmpty()) {
                    queryWrapper.in(AlertRecordPO::getStatus, statusList);
                }
            }

            // 创建时间范围
            if (conditions.containsKey("createTimeStart") && conditions.get("createTimeStart") instanceof LocalDateTime) {
                queryWrapper.ge(AlertRecordPO::getCreateTime, conditions.get("createTimeStart"));
            }
            if (conditions.containsKey("createTimeEnd") && conditions.get("createTimeEnd") instanceof LocalDateTime) {
                queryWrapper.le(AlertRecordPO::getCreateTime, conditions.get("createTimeEnd"));
            }

            // 更新时间范围
            if (conditions.containsKey("updateTimeStart") && conditions.get("updateTimeStart") instanceof LocalDateTime) {
                queryWrapper.ge(AlertRecordPO::getUpdateTime, conditions.get("updateTimeStart"));
            }
            if (conditions.containsKey("updateTimeEnd") && conditions.get("updateTimeEnd") instanceof LocalDateTime) {
                queryWrapper.le(AlertRecordPO::getUpdateTime, conditions.get("updateTimeEnd"));
            }

            // 预警值模糊查询
            if (conditions.containsKey("alertValueLike") && StringUtils.hasText((String) conditions.get("alertValueLike"))) {
                queryWrapper.like(AlertRecordPO::getAlertValue, conditions.get("alertValueLike"));
            }

            // 预警内容模糊查询
            if (conditions.containsKey("alertContentLike") && StringUtils.hasText((String) conditions.get("alertContentLike"))) {
                queryWrapper.like(AlertRecordPO::getAlertContent, conditions.get("alertContentLike"));
            }
        }

        // 默认按更新时间倒序
        queryWrapper.orderByDesc(AlertRecordPO::getUpdateTime);

        return alertRecordMapper.selectList(queryWrapper);
    }

    /**
     * 根据自定义条件查询记录列表且根据分组及排序仅查询每组前n条的数据
     *
     * @param conditions 查询条件Map
     * @param groupByField 分组字段（rule_code, alert_level, status等）
     * @param orderByField 排序字段（create_time, update_time等）
     * @param orderDesc 是否倒序
     * @param topN 每组取前N条
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listTopNByGroup(Map<String, Object> conditions, String groupByField,
                                               String orderByField, boolean orderDesc, int topN) {

        // 构建基础查询条件
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM (");
        sql.append("  SELECT *, ROW_NUMBER() OVER (");
        sql.append("    PARTITION BY ").append(groupByField);
        sql.append("    ORDER BY ").append(orderByField);
        if (orderDesc) {
            sql.append(" DESC");
        } else {
            sql.append(" ASC");
        }
        sql.append("  ) as rn");
        sql.append("  FROM alert_record");
        sql.append("  WHERE del_flag = '0'");

        // 添加动态条件
        if (conditions != null && !conditions.isEmpty()) {
            if (conditions.containsKey("ruleCode") && StringUtils.hasText((String) conditions.get("ruleCode"))) {
                sql.append(" AND rule_code = '").append(conditions.get("ruleCode")).append("'");
            }

            if (conditions.containsKey("ruleCodes") && conditions.get("ruleCodes") instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> ruleCodes = (List<String>) conditions.get("ruleCodes");
                if (!ruleCodes.isEmpty()) {
                    sql.append(" AND rule_code IN (");
                    for (int i = 0; i < ruleCodes.size(); i++) {
                        if (i > 0) sql.append(", ");
                        sql.append("'").append(ruleCodes.get(i)).append("'");
                    }
                    sql.append(")");
                }
            }

            if (conditions.containsKey("dedupValue") && StringUtils.hasText((String) conditions.get("dedupValue"))) {
                sql.append(" AND dedup_value = '").append(conditions.get("dedupValue")).append("'");
            }

            if (conditions.containsKey("dedupValues") && conditions.get("dedupValues") instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> dedupValues = (List<String>) conditions.get("dedupValues");
                if (!dedupValues.isEmpty()) {
                    sql.append(" AND dedup_value IN (");
                    for (int i = 0; i < dedupValues.size(); i++) {
                        if (i > 0) sql.append(", ");
                        sql.append("'").append(dedupValues.get(i)).append("'");
                    }
                    sql.append(")");
                }
            }

            if (conditions.containsKey("alertLevel") && StringUtils.hasText((String) conditions.get("alertLevel"))) {
                sql.append(" AND alert_level = '").append(conditions.get("alertLevel")).append("'");
            }

            if (conditions.containsKey("alertLevels") && conditions.get("alertLevels") instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> alertLevels = (List<String>) conditions.get("alertLevels");
                if (!alertLevels.isEmpty()) {
                    sql.append(" AND alert_level IN (");
                    for (int i = 0; i < alertLevels.size(); i++) {
                        if (i > 0) sql.append(", ");
                        sql.append("'").append(alertLevels.get(i)).append("'");
                    }
                    sql.append(")");
                }
            }

            if (conditions.containsKey("status") && conditions.get("status") != null) {
                sql.append(" AND status = ").append(conditions.get("status"));
            }

            if (conditions.containsKey("statusList") && conditions.get("statusList") instanceof List) {
                @SuppressWarnings("unchecked")
                List<Integer> statusList = (List<Integer>) conditions.get("statusList");
                if (!statusList.isEmpty()) {
                    sql.append(" AND status IN (");
                    for (int i = 0; i < statusList.size(); i++) {
                        if (i > 0) sql.append(", ");
                        sql.append(statusList.get(i));
                    }
                    sql.append(")");
                }
            }

            if (conditions.containsKey("changeType") && StringUtils.hasText((String) conditions.get("changeType"))) {
                sql.append(" AND change_type = '").append(conditions.get("changeType")).append("'");
            }

            if (conditions.containsKey("createTimeStart") && conditions.get("createTimeStart") instanceof LocalDateTime) {
                sql.append(" AND create_time >= '").append(conditions.get("createTimeStart")).append("'");
            }
            if (conditions.containsKey("createTimeEnd") && conditions.get("createTimeEnd") instanceof LocalDateTime) {
                sql.append(" AND create_time <= '").append(conditions.get("createTimeEnd")).append("'");
            }

            if (conditions.containsKey("updateTimeStart") && conditions.get("updateTimeStart") instanceof LocalDateTime) {
                sql.append(" AND update_time >= '").append(conditions.get("updateTimeStart")).append("'");
            }
            if (conditions.containsKey("updateTimeEnd") && conditions.get("updateTimeEnd") instanceof LocalDateTime) {
                sql.append(" AND update_time <= '").append(conditions.get("updateTimeEnd")).append("'");
            }
        }

        sql.append(") ranked WHERE rn <= ").append(topN);
        sql.append(" ORDER BY ").append(groupByField).append(", rn");

        // 使用原生SQL查询
        QueryWrapper<AlertRecordPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.apply(sql.toString());

        return alertRecordMapper.selectList(queryWrapper);
    }
}

package com.chis.zyjk.bigdata.alert.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chis.zyjk.bigdata.alert.mapper.AlertRecordMapper;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 预警记录服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertRecordService {

    private final AlertRecordMapper alertRecordMapper;

    /**
     * 根据ID查询预警记录
     *
     * @param id 主键ID
     * @return 预警记录
     */
    public AlertRecordPO getById(String id) {
        return alertRecordMapper.selectById(id);
    }

    /**
     * 根据规则编码和去重值查询预警记录
     *
     * @param ruleCode 规则编码
     * @param dedupValue 去重值
     * @return 预警记录
     */
    public AlertRecordPO getByRuleCodeAndDedupValue(String ruleCode, String dedupValue) {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordPO::getRuleCode, ruleCode)
                .eq(AlertRecordPO::getDedupValue, dedupValue)
                .eq(AlertRecordPO::getDelFlag, "0");
        return alertRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 根据规则编码查询预警记录
     *
     * @param ruleCode 规则编码
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listByRuleCode(String ruleCode) {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordPO::getRuleCode, ruleCode)
                .eq(AlertRecordPO::getDelFlag, "0")
                .orderByDesc(AlertRecordPO::getUpdateTime);
        return alertRecordMapper.selectList(queryWrapper);
    }

    /**
     * 查询未处置的预警记录
     *
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listUnprocessed() {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordPO::getStatus, 0)
                .eq(AlertRecordPO::getDelFlag, "0")
                .orderByDesc(AlertRecordPO::getCreateTime);
        return alertRecordMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询预警记录
     *
     * @param page 分页参数
     * @param ruleCode 规则编码（可选）
     * @param alertLevel 预警级别（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    public Page<AlertRecordPO> page(Page<AlertRecordPO> page, String ruleCode, String alertLevel, Integer status) {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ruleCode != null, AlertRecordPO::getRuleCode, ruleCode)
                .eq(alertLevel != null, AlertRecordPO::getAlertLevel, alertLevel)
                .eq(status != null, AlertRecordPO::getStatus, status)
                .eq(AlertRecordPO::getDelFlag, "0")
                .orderByDesc(AlertRecordPO::getUpdateTime);
        return alertRecordMapper.selectPage(page, queryWrapper);
    }

    /**
     * 保存预警记录
     *
     * @param alertRecord 预警记录
     * @return 是否成功
     */
    public boolean save(AlertRecordPO alertRecord) {
        return alertRecordMapper.insert(alertRecord) > 0;
    }

    /**
     * 更新预警记录
     *
     * @param alertRecord 预警记录
     * @return 是否成功
     */
    public boolean updateById(AlertRecordPO alertRecord) {
        return alertRecordMapper.updateById(alertRecord) > 0;
    }

    /**
     * 更新预警记录状态
     *
     * @param id 主键ID
     * @param status 状态
     * @return 是否成功
     */
    public boolean updateStatus(String id, Integer status) {
        LambdaUpdateWrapper<AlertRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AlertRecordPO::getStatus, status)
                .eq(AlertRecordPO::getId, id);
        return alertRecordMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 批量更新预警记录状态
     *
     * @param ids 主键ID列表
     * @param status 状态
     * @return 是否成功
     */
    public boolean batchUpdateStatus(List<String> ids, Integer status) {
        LambdaUpdateWrapper<AlertRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AlertRecordPO::getStatus, status)
                .in(AlertRecordPO::getId, ids);
        return alertRecordMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 批量新增预警记录
     *
     * @param alertRecords 预警记录列表
     * @return 是否成功
     */
    public boolean saveBatch(List<AlertRecordPO> alertRecords) {
        if (alertRecords == null || alertRecords.isEmpty()) {
            return true;
        }

        for (AlertRecordPO alertRecord : alertRecords) {
            alertRecordMapper.insert(alertRecord);
        }
        return true;
    }

    /**
     * 批量更新预警记录
     *
     * @param alertRecords 预警记录列表
     * @return 是否成功
     */
    public boolean updateBatchById(List<AlertRecordPO> alertRecords) {
        if (alertRecords == null || alertRecords.isEmpty()) {
            return true;
        }

        for (AlertRecordPO alertRecord : alertRecords) {
            alertRecordMapper.updateById(alertRecord);
        }
        return true;
    }

    /**
     * 根据自定义SQL条件查询记录列表
     *
     * @param whereSql WHERE子句SQL（不包含WHERE关键字），支持字段计算，如：rule_code = 'RULE001' AND (alert_value * 2) > 100
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listByCustomSql(String whereSql) {
        return alertRecordMapper.listByCustomSql(whereSql);
    }

    /**
     * 根据自定义SQL条件查询记录列表且根据分组及排序仅查询每组前n条的数据
     *
     * @param whereSql WHERE子句SQL（不包含WHERE关键字），支持字段计算，如：rule_code = 'RULE001' AND (alert_value * 2) > 100
     * @param groupByField 分组字段（rule_code, alert_level, status等）
     * @param orderByField 排序字段（create_time, update_time等）
     * @param orderDesc 是否倒序
     * @param topN 每组取前N条
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listTopNByCustomSql(String whereSql, String groupByField,
                                                   String orderByField, boolean orderDesc, int topN) {
        return alertRecordMapper.listTopNByCustomSql(whereSql, groupByField, orderByField, orderDesc, topN);
    }
}

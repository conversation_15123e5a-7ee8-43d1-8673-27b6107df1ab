package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONUtil;
import com.chis.zyjk.bigdata.alert.enums.DataProcessType;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeComponent;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertProcessResultDTO;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRecordDTO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import com.chis.zyjk.core.common.enums.base.StringCodeEnum;
import com.yomahub.liteflow.annotation.LiteflowComponent;

import java.util.ArrayList;
import java.util.List;

/**
 * 生成预警处理结果组件
 * <p>
 * <b>getAlertRecord</b>
 */
@LiteflowComponent(id = "genAlertProcess", name = "生成预警处理结果组件")
public class GenAlertProcessCmp extends ChisNodeComponent<AlertGlobalContext, AlertCmpContext> {

    @Override
    public void doProcess() {
        // 获取数据处理类型
        DataProcessType dataProcessType;
        try {
            dataProcessType = StringCodeEnum.fromCodeWithException(DataProcessType.class, data.getStr("type"));
        } catch (Exception e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_PARAMETER_ERROR,
                    this
            );
        }

        // 获取输入数据
        List<Object> inputDataList = getInputDataByPaths("inputDataPaths");

        AlertProcessResultDTO result = new AlertProcessResultDTO();
        if (ObjectUtil.isEmpty(inputDataList)) {
            outputResult(result);
            return;
        }

        // 数据处理方式
        switch (dataProcessType) {
            case BUSINESS:
                pakAlertProcessByBusiness(result, inputDataList);
                break;
            case ALERT:
                List<AlertRecordPO> alertRecordPOList = Convert.toList(AlertRecordPO.class, inputDataList);
                if (ObjectUtil.isEmpty(alertRecordPOList)) {
                    throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_TAG_MISSING, this);
                }
                pakAlertProcessByAlert(result, alertRecordPOList);
                break;
            default:
        }

        outputResult(result);
    }

    /**
     * 封装去重键值
     *
     * @param result        结果集
     * @param inputDataList 输入数据
     */
    private void pakAlertProcessByAlert(AlertProcessResultDTO result, List<AlertRecordPO> inputDataList) {
        // 封装去重键值
        for (AlertRecordPO alertRecordPO : inputDataList) {
            if (ObjectUtil.isEmpty(alertRecordPO)) {
                continue;
            }
            AlertRecordDTO record = new AlertRecordDTO();
            // 封装去重键值
            String deDupKey = alertRecordPO.getDedupValue();
            record.setDeDupKey(deDupKey);
            result.getDeduplicationKeyList().add(deDupKey);
            result.getAlertRecordMap().putIfAbsent(deDupKey, new ArrayList<>());
            result.getAlertRecordMap().get(deDupKey).add(record);
        }
    }

    /**
     * 封装业务数据
     *
     * @param result        结果集
     * @param inputDataList 输入数据
     */
    private void pakAlertProcessByBusiness(AlertProcessResultDTO result, List<Object> inputDataList) {
        // 获取预警规则编码
        String ruleCode = global.getRuleCode();
        // 获取去重键表达式 todo
        String deDupKeyExpression = global.getDeDupKeyExpression();
        // 获取预警值表达式
        String alertValueExpression = global.getAlertValueExpression();
        // 封装去重键值和预警值
        for (Object inputData : inputDataList) {
            if (ObjectUtil.isEmpty(inputData)) {
                continue;
            }
            ContextHelper.setContextData(this.getTag(), "optData", inputData, cmp);
            AlertRecordDTO record = new AlertRecordDTO();
            record.setRuleCode(ruleCode);
            // 封装去重键值
            try {
                String deDupKey = getValueByExpression(deDupKeyExpression);
                record.setDeDupKey(deDupKey);
                result.getDeduplicationKeyList().add(deDupKey);
                result.getAlertRecordMap().putIfAbsent(deDupKey, new ArrayList<>());
                result.getAlertRecordMap().get(deDupKey).add(record);
            } catch (Exception e) {
                throw LiteFlowExceptionHelper.createNodeException(
                        e,
                        LiteFlowErrorCode.EXPRESSION_ERROR,
                        LiteFlowErrorCode.EXPRESSION_ERROR + "，去重键表达式：[" + deDupKeyExpression + "]，：" + JSONUtil.toJsonStr(inputData),
                        this
                );
            }
            // 封装预警值
            if (StrUtil.isBlank(alertValueExpression)) {
                continue;
            }
            try {
                String alertValue = getValueByExpression(alertValueExpression);
                record.setAlertValue(alertValue);
            } catch (Exception e) {
                throw LiteFlowExceptionHelper.createNodeException(
                        e,
                        LiteFlowErrorCode.EXPRESSION_ERROR,
                        LiteFlowErrorCode.EXPRESSION_ERROR + "，预警值表达式：[" + deDupKeyExpression + "]，：" + JSONUtil.toJsonStr(inputData),
                        this
                );
            }
        }
    }

    /**
     * 根据表达式获取值
     *
     * @param expression 表达式
     * @return 值
     */
    private String getValueByExpression(String expression) {
        return VariableReplacerUtils.replaceVariables(this.tag, expression, contextBeanList);
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"type", "inputDataPaths", "queryWhereSql", "queryOrderBy", "queryGroupBy", "queryTopN"};
    }

    /**
     * 输出过滤结果
     *
     * @param result 过滤结果
     */
    public void outputResult(AlertProcessResultDTO result) {
        ContextHelper.setContextData(this.getTag(), "result", result, cmp);
    }

}

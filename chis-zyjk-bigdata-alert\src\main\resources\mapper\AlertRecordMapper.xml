<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.zyjk.bigdata.alert.mapper.AlertRecordMapper">

    <resultMap id="BaseResultMap" type="com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleCode" column="rule_code" jdbcType="VARCHAR"/>
        <result property="dedupValue" column="dedup_value" jdbcType="VARCHAR"/>
        <result property="changeType" column="change_type" jdbcType="VARCHAR"/>
        <result property="alertLevel" column="alert_level" jdbcType="VARCHAR"/>
        <result property="alertValue" column="alert_value" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="alertContent" column="alert_content" jdbcType="LONGVARCHAR"/>
        <result property="alertJson" column="alert_json" jdbcType="LONGVARCHAR"/>
        <result property="sourceData" column="source_data" jdbcType="LONGVARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, rule_code, dedup_value, change_type, alert_level, alert_value, status,
        alert_content, alert_json, source_data, revision, del_flag, create_time,
        create_by, update_time, update_by
    </sql>

    <!-- 根据自定义条件查询记录列表 -->
    <select id="listByConditions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_record
        WHERE del_flag = '0'
        <if test="conditions.ruleCode != null and conditions.ruleCode != ''">
            AND rule_code = #{conditions.ruleCode}
        </if>
        <if test="conditions.ruleCodes != null and conditions.ruleCodes.size() > 0">
            AND rule_code IN
            <foreach collection="conditions.ruleCodes" item="ruleCode" open="(" separator="," close=")">
                #{ruleCode}
            </foreach>
        </if>
        <if test="conditions.dedupValue != null and conditions.dedupValue != ''">
            AND dedup_value = #{conditions.dedupValue}
        </if>
        <if test="conditions.dedupValues != null and conditions.dedupValues.size() > 0">
            AND dedup_value IN
            <foreach collection="conditions.dedupValues" item="dedupValue" open="(" separator="," close=")">
                #{dedupValue}
            </foreach>
        </if>
        <if test="conditions.changeType != null and conditions.changeType != ''">
            AND change_type = #{conditions.changeType}
        </if>
        <if test="conditions.alertLevel != null and conditions.alertLevel != ''">
            AND alert_level = #{conditions.alertLevel}
        </if>
        <if test="conditions.alertLevels != null and conditions.alertLevels.size() > 0">
            AND alert_level IN
            <foreach collection="conditions.alertLevels" item="alertLevel" open="(" separator="," close=")">
                #{alertLevel}
            </foreach>
        </if>
        <if test="conditions.status != null">
            AND status = #{conditions.status}
        </if>
        <if test="conditions.statusList != null and conditions.statusList.size() > 0">
            AND status IN
            <foreach collection="conditions.statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="conditions.createTimeStart != null">
            AND create_time &gt;= #{conditions.createTimeStart}
        </if>
        <if test="conditions.createTimeEnd != null">
            AND create_time &lt;= #{conditions.createTimeEnd}
        </if>
        <if test="conditions.updateTimeStart != null">
            AND update_time &gt;= #{conditions.updateTimeStart}
        </if>
        <if test="conditions.updateTimeEnd != null">
            AND update_time &lt;= #{conditions.updateTimeEnd}
        </if>
        <if test="conditions.alertValueLike != null and conditions.alertValueLike != ''">
            AND alert_value LIKE CONCAT('%', #{conditions.alertValueLike}, '%')
        </if>
        <if test="conditions.alertContentLike != null and conditions.alertContentLike != ''">
            AND alert_content LIKE CONCAT('%', #{conditions.alertContentLike}, '%')
        </if>
        ORDER BY update_time DESC
    </select>

    <!-- 根据自定义条件查询记录列表且根据分组及排序仅查询每组前n条的数据 -->
    <select id="listTopNByGroup" resultMap="BaseResultMap">
        SELECT * FROM (
            SELECT <include refid="Base_Column_List"/>,
                   ROW_NUMBER() OVER (
                       PARTITION BY ${params.groupByField}
                       ORDER BY ${params.orderByField}
                       <if test="params.orderDesc">DESC</if>
                       <if test="!params.orderDesc">ASC</if>
                   ) as rn
            FROM alert_record
            WHERE del_flag = '0'
            <if test="params.conditions.ruleCode != null and params.conditions.ruleCode != ''">
                AND rule_code = #{params.conditions.ruleCode}
            </if>
            <if test="params.conditions.ruleCodes != null and params.conditions.ruleCodes.size() > 0">
                AND rule_code IN
                <foreach collection="params.conditions.ruleCodes" item="ruleCode" open="(" separator="," close=")">
                    #{ruleCode}
                </foreach>
            </if>
            <if test="params.conditions.dedupValue != null and params.conditions.dedupValue != ''">
                AND dedup_value = #{params.conditions.dedupValue}
            </if>
            <if test="params.conditions.dedupValues != null and params.conditions.dedupValues.size() > 0">
                AND dedup_value IN
                <foreach collection="params.conditions.dedupValues" item="dedupValue" open="(" separator="," close=")">
                    #{dedupValue}
                </foreach>
            </if>
            <if test="params.conditions.changeType != null and params.conditions.changeType != ''">
                AND change_type = #{params.conditions.changeType}
            </if>
            <if test="params.conditions.alertLevel != null and params.conditions.alertLevel != ''">
                AND alert_level = #{params.conditions.alertLevel}
            </if>
            <if test="params.conditions.alertLevels != null and params.conditions.alertLevels.size() > 0">
                AND alert_level IN
                <foreach collection="params.conditions.alertLevels" item="alertLevel" open="(" separator="," close=")">
                    #{alertLevel}
                </foreach>
            </if>
            <if test="params.conditions.status != null">
                AND status = #{params.conditions.status}
            </if>
            <if test="params.conditions.statusList != null and params.conditions.statusList.size() > 0">
                AND status IN
                <foreach collection="params.conditions.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="params.conditions.createTimeStart != null">
                AND create_time &gt;= #{params.conditions.createTimeStart}
            </if>
            <if test="params.conditions.createTimeEnd != null">
                AND create_time &lt;= #{params.conditions.createTimeEnd}
            </if>
            <if test="params.conditions.updateTimeStart != null">
                AND update_time &gt;= #{params.conditions.updateTimeStart}
            </if>
            <if test="params.conditions.updateTimeEnd != null">
                AND update_time &lt;= #{params.conditions.updateTimeEnd}
            </if>
        ) ranked
        WHERE rn &lt;= #{params.topN}
        ORDER BY ${params.groupByField}, rn
    </select>

</mapper>

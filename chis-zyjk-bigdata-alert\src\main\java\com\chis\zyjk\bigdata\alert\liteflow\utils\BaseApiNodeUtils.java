package com.chis.zyjk.bigdata.alert.liteflow.utils;

import cn.hutool.core.lang.Tuple;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.http.HttpRequest;
import com.chis.project.frame.common.tools.http.HttpUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.yomahub.liteflow.slot.DefaultContext;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API节点工具类
 * 提供API调用、数据提取、上下文管理等公共功能
 *
 * <AUTHOR> Assistant
 * @since 2024-01-17
 */
@Slf4j
public class BaseApiNodeUtils {

    /**
     * 构建API请求参数
     */
    public static Map<String, Object> buildApiParams(String tag, JSONObject config, List<Tuple> contextList) {
        Map<String, Object> params = new HashMap<>();

        // 添加基础参数
        JSONObject configParams = config.getJSONObject("params");
        if (configParams != null) {
            for (String key : configParams.keySet()) {
                Object value = configParams.get(key);
                if (value instanceof String) {
                    // 使用变量替换引擎处理参数值
                    String replacedValue = VariableReplacerUtils.replaceVariables(tag, (String) value, contextList);
                    params.put(key, replacedValue);
                } else {
                    params.put(key, value);
                }
            }
        }

        return params;
    }

    /**
     * 构建API请求参数（带分页）
     */
    public static Map<String, Object> buildApiParams(String tag, JSONObject config, int pageNum, List<Tuple> contextList) {
        Map<String, Object> params = buildApiParams(tag, config, contextList);

        // 添加分页参数
        JSONObject batchConfig = config.getJSONObject("batchConfig");
        if (batchConfig != null) {
            String pageNumField = batchConfig.getStr("pageNumField", "pageNum");
            String pageSizeField = batchConfig.getStr("pageSizeField", "pageSize");
            int pageSize = batchConfig.getInt("pageSize", 1000);

            params.put(pageNumField, pageNum);
            params.put(pageSizeField, pageSize);
        }

        return params;
    }

    /**
     * 构建API请求头
     */
    public static Map<String, String> buildApiHeaders(String tag, JSONObject config, List<Tuple> contextList) {
        Map<String, String> headers = new HashMap<>();

        // 默认请求头
        headers.put("Content-Type", "application/json");

        // 添加配置的请求头
        JSONObject configHeaders = config.getJSONObject("headers");
        if (configHeaders != null) {
            for (String key : configHeaders.keySet()) {
                Object value = configHeaders.get(key);
                if (value instanceof String) {
                    // 使用变量替换引擎处理请求头值
                    String replacedValue = VariableReplacerUtils.replaceVariables(tag, (String) value, contextList);
                    headers.put(key, replacedValue);
                } else {
                    headers.put(key, value.toString());
                }
            }
        }

        return headers;
    }

    /**
     * 执行API调用
     */
    public static String executeApiCall(String apiUrl, String method, Map<String, Object> params,
                                        Map<String, String> headers, int timeout, String tag) {
        String responseBody;
        try {
            if ("POST".equalsIgnoreCase(method)) {
                // POST请求使用JSON格式
                String jsonBody = new JSONObject(params).toString();
                responseBody = HttpRequest.post(apiUrl)
                        .addHeaders(headers)
                        .body(jsonBody)
                        .timeout(timeout)
                        .execute()
                        .body();
            } else {
                // GET请求使用参数形式
                responseBody = HttpUtil.get(apiUrl, params, timeout);
            }
        } catch (Exception e) {
            throw new RuntimeException("API调用失败，tag: " + tag + ", url: " + apiUrl + ", error: " + e.getMessage());
        }
        return responseBody;
    }

    /**
     * 保存响应数据到上下文（两级结构）
     */
    public static void saveResponseToContext(DefaultContext context, String tag, String responseBody) {
        JSONObject responseJson = new JSONObject(responseBody);
        // 更新当前数据
        ContextHelper.setContextData(tag, "responseData", responseJson, context);
    }

    /**
     * 提取结果数据
     */
    @SuppressWarnings("unchecked")
    public static List<Object> extractResultData(String responseBody, JSONObject config) {
        String resultField = config.getStr("resultField", "data");

        try {
            JSONObject responseJson = new JSONObject(responseBody);
            log.info("结果: {}", responseJson);
            Object resultData = VariableReplacerUtils.getNestedValue(responseJson, resultField);

            if (resultData == null) {
                log.warn("未找到结果数据字段: {}", resultField);
                return new ArrayList<>();
            }

            if (resultData instanceof List) {
                return (List<Object>) resultData;
            } else {
                // 单个对象包装为列表
                List<Object> result = new ArrayList<>();
                result.add(resultData);
                return result;
            }
        } catch (Exception e) {
            log.error("解析响应数据失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 更新当前数据到上下文
     */
    public static void updateCurrentDataToNodeHelper(String tag, List<Object> batchData, DefaultContext context) {
        Object lastItem;
        if (ObjectUtil.isEmpty(batchData)) {
            batchData = new ArrayList<>();
            lastItem = null;
        } else {
            lastItem = batchData.get(batchData.size() - 1);
        }
        // 更新当前数据
        ContextHelper.setContextData(tag, "currentData", batchData, context);
        // 更新当前数据最后一条记录
        ContextHelper.setContextData(tag, "currentLastItem", lastItem, context);

        log.debug("更新当前数据完成，tag: {}, dataSize: {}", tag, batchData.size());
    }
}

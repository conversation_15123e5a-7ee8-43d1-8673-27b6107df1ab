package com.chis.zyjk.bigdata.alert.liteflow.utils;

import cn.hutool.core.lang.Tuple;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.yomahub.liteflow.slot.DefaultContext;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SpEL表达式评估工具类
 * <p>提供强大的表达式解析和评估功能，支持：
 * <p>- 基本比较操作：==, !=, >, <, >=, <=
 * <p>- 逻辑操作：&&, ||, !
 * <p>- 字符串操作：contains, startsWith, endsWith, matches
 * <p>- 集合操作：in, not in
 * <p>- 嵌套属性访问：user.profile.name
 * <p>- 上下文变量：${global.config}, ${nodeId.data.field}
 */
@Slf4j
public final class SpELExpressionUtils {

    /**
     * 评估条件表达式
     *
     * @param condition 条件表达式(带点的参数从context中获取，不带点的参数就是inputData的属性)
     * @param context   LiteFlow上下文
     * @return 评估结果
     */
    public static boolean evaluateCondition(String condition, List<Tuple> context) {
        if (StrUtil.isBlank(condition)) {
            return true; // 空条件默认为true
        }

        // 解析并评估SpEL表达式
        try {
            return evaluateSpELExpression(condition, context);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage() + "，表达式: " + condition);
        }
    }

    /**
     * 评估SpEL表达式
     * 支持的语法：
     * - 简单属性访问：name, age, status
     * - 嵌套属性访问：user.name, order.customer.id
     * - 上下文变量访问：${global.config}, ${nodeId.data.field}
     * - 比较操作：==, !=, >, <, >=, <=
     * - 逻辑操作：&&, ||, !
     * - 字符串操作：contains, startsWith, endsWith
     * - 数值操作：+, -, *, /, %
     * - 集合操作：in, not in
     * - 正则匹配：matches
     *
     * @param expression SpEL表达式
     * @param context    LiteFlow上下文
     * @return 评估结果
     */
    private static boolean evaluateSpELExpression(String expression, List<Tuple> context) {
        // 解析并评估表达式
        return parseAndEvaluateExpression(expression, context);
    }

    /**
     * 解析并评估表达式
     * 支持基本的逻辑和比较操作
     *
     * @param expression 预处理后的表达式
     * @param context    上下文
     * @return 评估结果
     */
    private static boolean parseAndEvaluateExpression(String expression, List<Tuple> context) {
        expression = expression.trim();

        // 处理逻辑操作符（优先级：! > && > ||）
        if (expression.contains("||")) {
            return evaluateOrExpression(expression, context);
        }

        if (expression.contains("&&")) {
            return evaluateAndExpression(expression, context);
        }

        if (expression.startsWith("!")) {
            return !parseAndEvaluateExpression(expression.substring(1).trim(), context);
        }

        // 处理括号
        if (expression.startsWith("(") && expression.endsWith(")")) {
            return parseAndEvaluateExpression(expression.substring(1, expression.length() - 1), context);
        }

        // 处理比较操作
        return evaluateComparisonExpression(expression);
    }

    /**
     * 评估OR表达式
     */
    private static boolean evaluateOrExpression(String expression, List<Tuple> context) {
        String[] parts = splitByOperator(expression, "\\|\\|");
        for (String part : parts) {
            if (parseAndEvaluateExpression(part.trim(), context)) {
                return true; // OR操作，有一个为true就返回true
            }
        }
        return false;
    }

    /**
     * 评估AND表达式
     */
    private static boolean evaluateAndExpression(String expression, List<Tuple> context) {
        String[] parts = splitByOperator(expression, "&&");
        for (String part : parts) {
            if (!parseAndEvaluateExpression(part.trim(), context)) {
                return false; // AND操作，有一个为false就返回false
            }
        }
        return true;
    }

    /**
     * 分割操作符，考虑括号嵌套
     */
    private static String[] splitByOperator(String expression, String operator) {
        List<String> parts = new ArrayList<>();
        int start = 0;
        int parentheses = 0;

        Pattern pattern = Pattern.compile(operator);
        Matcher matcher = pattern.matcher(expression);

        while (matcher.find()) {
            // 检查当前位置是否在括号内
            String beforeMatch = expression.substring(start, matcher.start());
            for (char c : beforeMatch.toCharArray()) {
                if (c == '(') parentheses++;
                else if (c == ')') parentheses--;
            }

            if (parentheses == 0) {
                parts.add(expression.substring(start, matcher.start()));
                start = matcher.end();
            }
        }

        parts.add(expression.substring(start));
        return parts.toArray(new String[0]);
    }

    /**
     * 评估比较表达式
     */
    private static boolean evaluateComparisonExpression(String expression) {
        // 支持的比较操作符（按长度排序，避免匹配错误）
        String[] operators = {">=", "<=", "!=", "==", ">", "<", "contains", "startsWith", "endsWith", "matches", "in", "not in"};

        for (String operator : operators) {
            if (expression.contains(operator)) {
                String[] parts = expression.split(Pattern.quote(operator), 2);
                if (parts.length == 2) {
                    String leftExpr = parts[0].trim();
                    String rightExpr = parts[1].trim();

                    Object leftValue = getExpressionValue(leftExpr);
                    Object rightValue = getExpressionValue(rightExpr);

                    return compareValues(leftValue, operator, rightValue);
                }
            }
        }

        // 如果没有比较操作符，尝试作为布尔值或存在性检查
        Object value = getExpressionValue(expression);
        return toBooleanValue(value);
    }

    /**
     * 获取表达式的值
     */
    private static Object getExpressionValue(String expression) {
        expression = expression.trim();

        // 移除引号（字符串字面量）
        if ((expression.startsWith("'") && expression.endsWith("'")) ||
                (expression.startsWith("\"") && expression.endsWith("\""))) {
            return expression.substring(1, expression.length() - 1);
        }

        // 数值字面量
        if (expression.matches("-?\\d+")) {
            return Long.parseLong(expression);
        }
        if (expression.matches("-?\\d+\\.\\d+")) {
            return Double.parseDouble(expression);
        }

        // 布尔字面量
        if ("true".equalsIgnoreCase(expression)) {
            return true;
        }
        if ("false".equalsIgnoreCase(expression)) {
            return false;
        }

        // null字面量
        if ("null".equalsIgnoreCase(expression)) {
            return null;
        }

        // 直接返回
        return expression;
    }

    /**
     * 获取字段值（支持嵌套属性访问）
     */
    private static Object getFieldValue(Object data, String fieldPath, DefaultContext context) {
        if (StrUtil.isBlank(fieldPath) || data == null) {
            return null;
        }

        String[] parts = fieldPath.split("\\.");
        Object current = data;

        for (String part : parts) {
            if (current == null) {
                return null;
            }

            if (current instanceof JSONObject) {
                current = ((JSONObject) current).get(part);
            } else if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(part);
            } else {
                // 尝试通过反射获取属性值
                current = getPropertyValue(current, part);
            }
        }

        return current;
    }

    /**
     * 通过反射获取属性值
     */
    private static Object getPropertyValue(Object obj, String propertyName) {
        if (obj == null || StrUtil.isBlank(propertyName)) {
            return null;
        }

        try {
            // 尝试getter方法
            String getterName = "get" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
            java.lang.reflect.Method getter = obj.getClass().getMethod(getterName);
            return getter.invoke(obj);
        } catch (Exception e) {
            try {
                // 尝试is方法（布尔类型）
                String isMethodName = "is" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
                java.lang.reflect.Method isMethod = obj.getClass().getMethod(isMethodName);
                return isMethod.invoke(obj);
            } catch (Exception e2) {
                try {
                    // 尝试直接访问字段
                    java.lang.reflect.Field field = obj.getClass().getDeclaredField(propertyName);
                    field.setAccessible(true);
                    return field.get(obj);
                } catch (Exception e3) {
                    return null;
                }
            }
        }
    }

    /**
     * 比较两个值
     */
    private static boolean compareValues(Object leftValue, String operator, Object rightValue) {
        switch (operator) {
            case "==":
                return objectEquals(leftValue, rightValue);
            case "!=":
                return !objectEquals(leftValue, rightValue);
            case ">":
                return compareNumbers(leftValue, rightValue) > 0;
            case "<":
                return compareNumbers(leftValue, rightValue) < 0;
            case ">=":
                return compareNumbers(leftValue, rightValue) >= 0;
            case "<=":
                return compareNumbers(leftValue, rightValue) <= 0;
            case "contains":
                return stringContains(leftValue, rightValue);
            case "startsWith":
                return stringStartsWith(leftValue, rightValue);
            case "endsWith":
                return stringEndsWith(leftValue, rightValue);
            case "matches":
                return stringMatches(leftValue, rightValue);
            case "in":
                return valueInCollection(leftValue, rightValue);
            case "not in":
                return !valueInCollection(leftValue, rightValue);
            default:
                return false;
        }
    }

    /**
     * 对象相等比较
     */
    private static boolean objectEquals(Object left, Object right) {
        if (left == null && right == null) {
            return true;
        }
        if (left == null || right == null) {
            return false;
        }

        // 字符串比较
        if (left instanceof String || right instanceof String) {
            return String.valueOf(left).equals(String.valueOf(right));
        }

        // 数值比较
        if (left instanceof Number && right instanceof Number) {
            return compareNumbers(left, right) == 0;
        }

        return left.equals(right);
    }

    /**
     * 数值比较
     */
    private static int compareNumbers(Object left, Object right) {
        if (!(left instanceof Number) || !(right instanceof Number)) {
            throw new IllegalArgumentException("无法比较非数值类型: " + left + " 和 " + right);
        }

        double leftDouble = ((Number) left).doubleValue();
        double rightDouble = ((Number) right).doubleValue();

        return Double.compare(leftDouble, rightDouble);
    }

    /**
     * 字符串包含检查
     */
    private static boolean stringContains(Object left, Object right) {
        if (left == null || right == null) {
            return false;
        }
        return String.valueOf(left).contains(String.valueOf(right));
    }

    /**
     * 字符串开始检查
     */
    private static boolean stringStartsWith(Object left, Object right) {
        if (left == null || right == null) {
            return false;
        }
        return String.valueOf(left).startsWith(String.valueOf(right));
    }

    /**
     * 字符串结束检查
     */
    private static boolean stringEndsWith(Object left, Object right) {
        if (left == null || right == null) {
            return false;
        }
        return String.valueOf(left).endsWith(String.valueOf(right));
    }

    /**
     * 正则匹配检查
     */
    private static boolean stringMatches(Object left, Object right) {
        if (left == null || right == null) {
            return false;
        }
        try {
            return String.valueOf(left).matches(String.valueOf(right));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 值在集合中检查
     */
    private static boolean valueInCollection(Object value, Object collection) {
        if (collection == null) {
            return false;
        }

        if (collection instanceof List) {
            return ((List<?>) collection).contains(value);
        }

        if (collection instanceof Object[]) {
            Object[] array = (Object[]) collection;
            for (Object item : array) {
                if (objectEquals(value, item)) {
                    return true;
                }
            }
        }

        // 尝试将字符串解析为数组
        if (collection instanceof String) {
            String collectionStr = (String) collection;
            if (collectionStr.startsWith("[") && collectionStr.endsWith("]")) {
                String content = collectionStr.substring(1, collectionStr.length() - 1);
                String[] items = content.split(",");
                for (String item : items) {
                    if (objectEquals(value, item.trim())) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 转换为布尔值
     */
    private static boolean toBooleanValue(Object value) {
        if (value == null) {
            throw new RuntimeException("比较内容类型仅支持Boolean类型[null]");
        }

        if (value instanceof Boolean) {
            return (Boolean) value;
        }
//
//        if (value instanceof Number) {
//            return ((Number) value).doubleValue() != 0;
//        }
//
//        if (value instanceof String) {
//            String str = (String) value;
//            return !str.isEmpty() && !"false".equalsIgnoreCase(str) && !"0".equals(str);
//        }
        throw new RuntimeException("比较内容类型仅支持Boolean类型[" + value + "]");
    }
}

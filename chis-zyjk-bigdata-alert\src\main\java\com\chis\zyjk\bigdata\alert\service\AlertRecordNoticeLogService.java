package com.chis.zyjk.bigdata.alert.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chis.zyjk.bigdata.alert.mapper.AlertRecordNoticeLogMapper;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordNoticeLogPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 预警记录通知子服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertRecordNoticeLogService {

    private final AlertRecordNoticeLogMapper alertRecordNoticeLogMapper;

    /**
     * 根据ID查询通知日志
     *
     * @param id 主键ID
     * @return 通知日志
     */
    public AlertRecordNoticeLogPO getById(String id) {
        return alertRecordNoticeLogMapper.selectById(id);
    }

    /**
     * 根据预警记录ID查询通知日志列表
     *
     * @param alertRecordId 预警记录ID
     * @return 通知日志列表
     */
    public List<AlertRecordNoticeLogPO> listByAlertRecordId(String alertRecordId) {
        LambdaQueryWrapper<AlertRecordNoticeLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordNoticeLogPO::getAlertRecordId, alertRecordId)
                .eq(AlertRecordNoticeLogPO::getDelFlag, "0")
                .orderByDesc(AlertRecordNoticeLogPO::getCreateTime);
        return alertRecordNoticeLogMapper.selectList(queryWrapper);
    }

    /**
     * 根据预警记录日志ID查询通知日志列表
     *
     * @param alertRecordLogId 预警记录日志ID
     * @return 通知日志列表
     */
    public List<AlertRecordNoticeLogPO> listByAlertRecordLogId(String alertRecordLogId) {
        LambdaQueryWrapper<AlertRecordNoticeLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordNoticeLogPO::getAlertRecordLogId, alertRecordLogId)
                .eq(AlertRecordNoticeLogPO::getDelFlag, "0")
                .orderByDesc(AlertRecordNoticeLogPO::getCreateTime);
        return alertRecordNoticeLogMapper.selectList(queryWrapper);
    }

    /**
     * 根据规则编码查询通知日志列表
     *
     * @param ruleCode 规则编码
     * @return 通知日志列表
     */
    public List<AlertRecordNoticeLogPO> listByRuleCode(String ruleCode) {
        LambdaQueryWrapper<AlertRecordNoticeLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordNoticeLogPO::getRuleCode, ruleCode)
                .eq(AlertRecordNoticeLogPO::getDelFlag, "0")
                .orderByDesc(AlertRecordNoticeLogPO::getCreateTime);
        return alertRecordNoticeLogMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询通知日志
     *
     * @param page 分页参数
     * @param alertRecordId 预警记录ID（可选）
     * @param alertRecordLogId 预警记录日志ID（可选）
     * @param ruleCode 规则编码（可选）
     * @param changeType 变更类型（可选）
     * @return 分页结果
     */
    public Page<AlertRecordNoticeLogPO> page(Page<AlertRecordNoticeLogPO> page, String alertRecordId, 
                                             String alertRecordLogId, String ruleCode, String changeType) {
        LambdaQueryWrapper<AlertRecordNoticeLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(alertRecordId != null, AlertRecordNoticeLogPO::getAlertRecordId, alertRecordId)
                .eq(alertRecordLogId != null, AlertRecordNoticeLogPO::getAlertRecordLogId, alertRecordLogId)
                .like(ruleCode != null, AlertRecordNoticeLogPO::getRuleCode, ruleCode)
                .eq(changeType != null, AlertRecordNoticeLogPO::getChangeType, changeType)
                .eq(AlertRecordNoticeLogPO::getDelFlag, "0")
                .orderByDesc(AlertRecordNoticeLogPO::getCreateTime);
        return alertRecordNoticeLogMapper.selectPage(page, queryWrapper);
    }

    /**
     * 保存通知日志
     *
     * @param noticeLog 通知日志
     * @return 是否成功
     */
    public boolean save(AlertRecordNoticeLogPO noticeLog) {
        return alertRecordNoticeLogMapper.insert(noticeLog) > 0;
    }

    /**
     * 批量保存通知日志
     *
     * @param noticeLogs 通知日志列表
     * @return 是否成功
     */
    public boolean saveBatch(List<AlertRecordNoticeLogPO> noticeLogs) {
        if (noticeLogs == null || noticeLogs.isEmpty()) {
            return true;
        }

        for (AlertRecordNoticeLogPO noticeLog : noticeLogs) {
            alertRecordNoticeLogMapper.insert(noticeLog);
        }
        return true;
    }

    /**
     * 更新通知日志
     *
     * @param noticeLog 通知日志
     * @return 是否成功
     */
    public boolean updateById(AlertRecordNoticeLogPO noticeLog) {
        return alertRecordNoticeLogMapper.updateById(noticeLog) > 0;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.chis.zyjk</groupId>
        <artifactId>chis-zyjk-frame</artifactId>
        <version>1.0.0.ALPHA</version>
        <relativePath/>
    </parent>

    <artifactId>chis-zyjk-bigdata-platform</artifactId>
    <version>1.0.0.ALPHA</version>
    <packaging>pom</packaging>

    <modules>
        <module>chis-zyjk-bigdata-timer</module>
        <module>chis-zyjk-bigdata-api-dataserver</module>
        <module>chis-zyjk-bigdata-common</module>
        <module>chis-zyjk-bigdata-api-dataserver</module>
        <module>chis-zyjk-bigdata-common</module>
        <module>chis-zyjk-bigdata-timer</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <druid.version>1.2.16</druid.version>
        <mysql.version>8.0.28</mysql.version>
        <fastjson.version>1.2.83</fastjson.version>
        <hutool.version>5.8.38</hutool.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
    </properties>
</project>
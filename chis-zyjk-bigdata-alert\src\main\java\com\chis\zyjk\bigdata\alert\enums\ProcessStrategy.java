package com.chis.zyjk.bigdata.alert.enums;

import com.chis.zyjk.core.common.enums.base.StringCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预警处理策略
 */
@Getter
@AllArgsConstructor
public enum ProcessStrategy implements StringCodeEnum {
    /**
     * 新增
     */
    CREATE("CREATE", "新增"),
    /**
     * 更新
     */
    UPDATE("UPDATE", "更新"),
    /**
     * 处置
     */
    DISPOSE("DISPOSE", "处置"),
    /**
     * 忽略
     */
    IGNORE("IGNORE", "忽略"),
    /**
     * 不处理
     */
    NO_ACTION("NO_ACTION", "不处理");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    @Override
    public String getEnumDesc() {
        return "预警处理策略";
    }
}
